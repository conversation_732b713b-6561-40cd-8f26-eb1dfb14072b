import * as fs from 'fs';
import * as path from 'path';
import { BcutASR } from '../utils/asr/BcutASR';
import { ASRData } from '../utils/asr/ASRData';

export interface ASROptions {
  language: string;
  outputFormats: string[];
  outputDirectory?: string;
  outputPaths?: string[];
}

export interface ASRResult {
  text: string;
  srt: string;
  vtt?: string;
  json?: string;
  segments?: any[];
  statistics?: any;
}

export class ASRService {
  private tempDir: string;

  constructor() {
    this.tempDir = path.join(__dirname, '../../temp/asr');

    // 确保临时目录存在
    if (!fs.existsSync(this.tempDir)) {
      fs.mkdirSync(this.tempDir, { recursive: true });
    }
  }

  /**
   * 从音频文件提取文字内容
   */
  async extractText(audioPath: string, options: ASROptions): Promise<ASRResult> {
    console.log(`[ASRService] 开始语音识别: ${audioPath}`);

    if (!fs.existsSync(audioPath)) {
      throw new Error(`音频文件不存在: ${audioPath}`);
    }

    try {
      // 创建BcutASR实例
      const asr = new BcutASR(audioPath, true, options.language);

      // 执行ASR识别
      const asrData = await asr.run();

      if (!asrData || !asrData.hasData()) {
        throw new Error('语音识别未返回有效结果');
      }

      console.log(`[ASRService] 识别完成，共 ${asrData.segments.length} 个片段`);

      // 准备结果
      const result: ASRResult = {
        text: asrData.toTxt(),
        srt: asrData.toSrt()
      };

      // 根据选项添加其他格式
      if (options.outputFormats.includes('vtt')) {
        result.vtt = asrData.toVtt();
      }

      if (options.outputFormats.includes('json')) {
        result.json = asrData.toJson();
      }

      if (options.outputFormats.includes('segments')) {
        result.segments = asrData.segments.map(seg => ({
          start: seg.start,
          end: seg.end,
          text: seg.text,
          duration: seg.getDuration()
        }));
      }

      if (options.outputFormats.includes('statistics')) {
        result.statistics = asrData.getStatistics();
      }

      // 如果指定了输出目录，自动保存文件
      if (options.outputDirectory) {
        await this.saveResultFiles(audioPath, result, options);
      }

      return result;
    } catch (error: unknown) {
      console.error(`[ASRService] 语音识别失败:`, error);
      throw new Error(`语音识别失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 从视频文件提取音频并进行语音识别
   */
  async extractTextFromVideo(videoPath: string, options: ASROptions): Promise<ASRResult> {
    console.log(`[ASRService] 从视频提取音频并识别: ${videoPath}`);

    if (!fs.existsSync(videoPath)) {
      throw new Error(`视频文件不存在: ${videoPath}`);
    }

    // 首先需要从视频中提取音频
    const audioPath = await this.extractAudioFromVideo(videoPath);

    try {
      // 执行语音识别
      const result = await this.extractText(audioPath, options);

      // 清理临时音频文件
      if (fs.existsSync(audioPath)) {
        fs.unlinkSync(audioPath);
      }

      // 如果指定了输出目录，自动保存文件
      if (options.outputDirectory) {
        await this.saveResultFiles(videoPath, result, options);
      }

      return result;
    } catch (error) {
      // 确保清理临时文件
      if (fs.existsSync(audioPath)) {
        fs.unlinkSync(audioPath);
      }
      throw error;
    }
  }

  /**
   * 保存识别结果到文件
   */
  private async saveResultFiles(inputPath: string, result: ASRResult, options: ASROptions): Promise<void> {
    if (!options.outputDirectory) {
      return;
    }

    try {
      // 确保输出目录存在
      if (!fs.existsSync(options.outputDirectory)) {
        fs.mkdirSync(options.outputDirectory, { recursive: true });
      }

      const baseName = path.basename(inputPath, path.extname(inputPath));

      // 保存文本文件
      if (options.outputFormats.includes('text') && result.text) {
        const txtPath = path.join(options.outputDirectory, `${baseName}.txt`);
        fs.writeFileSync(txtPath, result.text, 'utf8');
        console.log(`[ASRService] 文本文件已保存: ${txtPath}`);
      }

      // 保存SRT字幕文件
      if (options.outputFormats.includes('srt') && result.srt) {
        const srtPath = path.join(options.outputDirectory, `${baseName}.srt`);
        fs.writeFileSync(srtPath, result.srt, 'utf8');
        console.log(`[ASRService] SRT字幕文件已保存: ${srtPath}`);
      }

      // 保存VTT字幕文件
      if (options.outputFormats.includes('vtt') && result.vtt) {
        const vttPath = path.join(options.outputDirectory, `${baseName}.vtt`);
        fs.writeFileSync(vttPath, result.vtt, 'utf8');
        console.log(`[ASRService] VTT字幕文件已保存: ${vttPath}`);
      }

      // 保存JSON格式的详细数据
      if (options.outputFormats.includes('json')) {
        const jsonPath = path.join(options.outputDirectory, `${baseName}.json`);
        const jsonData = {
          text: result.text,
          srt: result.srt,
          vtt: result.vtt,
          segments: result.segments,
          statistics: result.statistics,
          metadata: {
            inputFile: inputPath,
            language: options.language,
            processedAt: new Date().toISOString()
          }
        };
        fs.writeFileSync(jsonPath, JSON.stringify(jsonData, null, 2), 'utf8');
        console.log(`[ASRService] JSON数据文件已保存: ${jsonPath}`);
      }

    } catch (error) {
      console.error(`[ASRService] 保存结果文件失败:`, error);
      throw new Error(`保存结果文件失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 从视频文件提取音频（简化版本，实际应该调用FFmpegService）
   */
  private async extractAudioFromVideo(videoPath: string): Promise<string> {
    // 这里应该调用FFmpegService来提取音频
    // 为了简化，我们假设已经有了这个功能
    const fileName = path.basename(videoPath, path.extname(videoPath));
    const audioPath = path.join(this.tempDir, `${fileName}_extracted.wav`);

    // TODO: 实际应该调用 FFmpegService.extractAudio
    // await this.ffmpegService.extractAudio(videoPath, audioPath);

    throw new Error('从视频提取音频功能需要集成FFmpegService');
  }

  /**
   * 批量处理音频文件
   */
  async batchExtractText(audioPaths: string[], options: ASROptions): Promise<{ [audioPath: string]: ASRResult | Error }> {
    console.log(`[ASRService] 批量语音识别: ${audioPaths.length} 个文件`);

    const results: { [audioPath: string]: ASRResult | Error } = {};

    // 串行处理，避免同时发起太多API请求
    for (const audioPath of audioPaths) {
      try {
        console.log(`[ASRService] 处理文件: ${audioPath}`);
        const result = await this.extractText(audioPath, options);
        results[audioPath] = result;
      } catch (error: unknown) {
        console.error(`[ASRService] 处理文件失败: ${audioPath}`, error);
        results[audioPath] = error instanceof Error ? error : new Error('未知错误');
      }

      // 添加延迟避免API限制
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    return results;
  }

  /**
   * 保存识别结果到文件
   */
  async saveResultToFile(result: ASRResult, outputDir: string, baseName: string): Promise<string[]> {
    console.log(`[ASRService] 保存识别结果到: ${outputDir}`);

    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    const savedFiles: string[] = [];

    // 保存文本文件
    if (result.text) {
      const txtPath = path.join(outputDir, `${baseName}.txt`);
      fs.writeFileSync(txtPath, result.text, 'utf-8');
      savedFiles.push(txtPath);
    }

    // 保存SRT字幕文件
    if (result.srt) {
      const srtPath = path.join(outputDir, `${baseName}.srt`);
      fs.writeFileSync(srtPath, result.srt, 'utf-8');
      savedFiles.push(srtPath);
    }

    // 保存VTT字幕文件
    if (result.vtt) {
      const vttPath = path.join(outputDir, `${baseName}.vtt`);
      fs.writeFileSync(vttPath, result.vtt, 'utf-8');
      savedFiles.push(vttPath);
    }

    // 保存JSON文件
    if (result.json) {
      const jsonPath = path.join(outputDir, `${baseName}_data.json`);
      fs.writeFileSync(jsonPath, result.json, 'utf-8');
      savedFiles.push(jsonPath);
    }

    console.log(`[ASRService] 保存完成，共 ${savedFiles.length} 个文件`);
    return savedFiles;
  }

  /**
   * 获取支持的语言列表
   */
  getSupportedLanguages(): { code: string; name: string }[] {
    return [
      { code: 'zh', name: '中文' },
      { code: 'en', name: '英文' },
      { code: 'auto', name: '自动检测' }
    ];
  }

  /**
   * 获取支持的输出格式
   */
  getSupportedOutputFormats(): { code: string; name: string; description: string }[] {
    return [
      { code: 'txt', name: '纯文本', description: '识别结果的纯文本格式' },
      { code: 'srt', name: 'SRT字幕', description: '带时间轴的SRT字幕文件' },
      { code: 'vtt', name: 'VTT字幕', description: '网页标准的VTT字幕文件' },
      { code: 'json', name: 'JSON数据', description: '包含详细信息的JSON格式' },
      { code: 'segments', name: '片段数据', description: '分段的详细数据' },
      { code: 'statistics', name: '统计信息', description: '识别结果的统计数据' }
    ];
  }

  /**
   * 验证音频文件格式
   */
  validateAudioFile(audioPath: string): { valid: boolean; error?: string; info?: any } {
    try {
      if (!fs.existsSync(audioPath)) {
        return { valid: false, error: '文件不存在' };
      }

      const stats = fs.statSync(audioPath);
      const ext = path.extname(audioPath).toLowerCase();
      const supportedFormats = ['.mp3', '.wav', '.m4a', '.aac', '.flac', '.ogg'];

      if (!supportedFormats.includes(ext)) {
        return {
          valid: false,
          error: `不支持的音频格式: ${ext}。支持的格式: ${supportedFormats.join(', ')}`
        };
      }

      const maxSize = 100 * 1024 * 1024; // 100MB
      if (stats.size > maxSize) {
        return {
          valid: false,
          error: `文件过大: ${(stats.size / 1024 / 1024).toFixed(2)}MB，最大支持 ${maxSize / 1024 / 1024}MB`
        };
      }

      return {
        valid: true,
        info: {
          fileName: path.basename(audioPath),
          fileSize: stats.size,
          format: ext.slice(1),
          lastModified: stats.mtime
        }
      };
    } catch (error: unknown) {
      return { valid: false, error: `文件验证失败: ${error instanceof Error ? error.message : '未知错误'}` };
    }
  }

  /**
   * 清理临时文件
   */
  async cleanupTempFiles(): Promise<void> {
    try {
      if (fs.existsSync(this.tempDir)) {
        const files = fs.readdirSync(this.tempDir);
        for (const file of files) {
          const filePath = path.join(this.tempDir, file);
          const stats = fs.statSync(filePath);

          // 删除超过1小时的临时文件
          if (Date.now() - stats.mtime.getTime() > 60 * 60 * 1000) {
            fs.unlinkSync(filePath);
            console.log(`[ASRService] 清理临时文件: ${filePath}`);
          }
        }
      }
    } catch (error) {
      console.error(`[ASRService] 清理临时文件失败:`, error);
    }
  }

  /**
   * 估算处理时间
   */
  estimateProcessingTime(audioPath: string): number {
    try {
      const stats = fs.statSync(audioPath);
      const fileSizeMB = stats.size / 1024 / 1024;

      // 粗略估算：每MB大约需要10秒处理时间
      return Math.ceil(fileSizeMB * 10);
    } catch (error) {
      console.error(`[ASRService] 估算处理时间失败:`, error);
      return 60; // 默认1分钟
    }
  }
}