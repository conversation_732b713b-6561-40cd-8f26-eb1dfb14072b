<template>
  <div class="app-card asr-processor">
    <div class="section-header">
      <h3>
        <Icon icon="mdi:microphone" class="section-icon" />
        音视频语音识别
      </h3>
      <el-button size="small" @click="clearAll" v-if="fileList.length > 0">
        <Icon icon="mdi:trash-can-outline" />
        清空
      </el-button>
    </div>

    <!-- 文件上传 -->
    <FileUploader v-model="fileList" task-type="asr" :max-file-size="500" upload-text="上传音频或视频文件进行语音识别"
      hint="音频格式：MP3, WAV, M4A, AAC, FLAC；视频格式：MP4, AVI, MOV, MKV, FLV, WMV" @file-added="handleFileAdded"
      @file-removed="handleFileRemoved" @error="handleError" />

    <!-- 识别设置 -->
    <div class="settings-section" v-if="fileList.length > 0">
      <el-divider>识别设置</el-divider>
      <ProcessingOptions v-model="processingOptions" task-type="asr" :show-presets="true" :show-save-preset="true"
        @preset-saved="handlePresetSaved" />

      <!-- 视频文件特殊提示 -->
      <div class="video-info-panel" v-if="hasVideoFiles">
        <el-alert title="视频文件处理提示" type="info" :closable="false" show-icon>
          <template #default>
            <div class="info-content">
              <div class="info-item">
                <Icon icon="mdi:clock" />
                <span>视频文件会先提取音频，耗时较长</span>
              </div>
              <div class="info-item">
                <Icon icon="mdi:harddisk" />
                <span>处理过程中会产生临时文件</span>
              </div>
            </div>
          </template>
        </el-alert>
      </div>
    </div>

    <!-- 输出目录 -->
    <OutputDirectorySelector v-model="outputDirectory" v-if="fileList.length > 0" label="识别结果输出目录"
      @directory-selected="handleDirectorySelected" @error="handleError" />

    <!-- 任务控制 -->
    <TaskControls v-if="fileList.length > 0" mode="single" :status="taskStatus" :is-processing="isRecognizing"
      :can-start="canStartRecognition" :can-pause="isRecognizing" :can-stop="isRecognizing" :show-batch-controls="false"
      :show-advanced="false" start-button-text="开始识别" processing-button-text="识别中..." @start="startRecognition"
      @pause="pauseRecognition" @stop="stopRecognition" />

    <!-- 识别进度 -->
    <div class="progress-section" v-if="recognitionTasks.length > 0">
      <el-divider>识别进度</el-divider>
      <TaskProgress :tasks="recognitionTasks" :allow-retry="true" :allow-pause="true" :allow-remove="true"
        :show-summary="recognitionTasks.length > 1" :show-result-info="true" @retry="retryTask" @pause="pauseTask"
        @remove="removeTask" @view-result="viewASRResult" />
    </div>

    <!-- ASR结果面板 -->
    <div class="results-section" v-if="asrResults.length > 0">
      <el-divider>识别结果</el-divider>
      <TaskResultPanel :results="asrResults" :allow-retry="true" empty-text="暂无识别结果" empty-hint="完成识别后结果将显示在这里"
        @retry="retryTaskFromResult" @remove="removeResult" @export-all="exportAllResults" />
    </div>

    <!-- 当前结果展示 -->
    <div class="current-result-section" v-if="currentResult">
      <el-divider>当前识别结果</el-divider>
      <ASRResultViewer :result="currentResult" :output-directory="outputDirectory" @copy-text="copyToClipboard"
        @save-file="saveResultToFile" @close="currentResult = null" />
    </div>

    <!-- 批量操作 -->
    <div class="batch-section" v-if="fileList.length > 1">
      <el-divider>批量操作</el-divider>
      <div class="batch-actions">
        <el-button @click="createBatchTask">
          <Icon icon="mdi:playlist-plus" />
          创建批量任务
        </el-button>
        <el-button @click="exportAllResults" :disabled="asrResults.length === 0">
          <Icon icon="mdi:export" />
          导出所有结果
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Icon } from '@iconify/vue'
import type { UploadFile } from 'element-plus'

// 导入共享组件
import FileUploader from './shared/FileUploader.vue'
import OutputDirectorySelector from './shared/OutputDirectorySelector.vue'
import ProcessingOptions from './shared/ProcessingOptions.vue'
import TaskControls from './shared/TaskControls.vue'
import TaskProgress from './shared/TaskProgress.vue'
import TaskResultPanel from './shared/TaskResultPanel.vue'

// 导入Store
import { useMediaStore } from '@/stores/media-store'
import type { ProcessingOptions as ProcessingOptionsType } from '@/stores/media-store'

// 定义事件
const emit = defineEmits<{
  fileUploaded: [fileInfo: any]
  recognitionStarted: [taskInfo: any]
  recognitionCompleted: [result: any]
}>()

// 响应式数据
const mediaStore = useMediaStore()
const fileList = ref<UploadFile[]>([])
const outputDirectory = ref('')
const recognitionTasks = ref<any[]>([])
const asrResults = ref<any[]>([])
const currentResult = ref<any>(null)
const isRecognizing = ref(false)
const filePathMap = ref<Map<string, string>>(new Map())
const taskWatchers = ref<Map<string, () => void>>(new Map())

// 识别设置
const processingOptions = reactive<ProcessingOptionsType>({
  language: 'zh',
  outputFormats: ['txt', 'srt'],
  audioQuality: 'high',
  enableTimestamps: true,
  filterSilence: false
})

// 计算属性
const taskStatus = computed(() => {
  if (isRecognizing.value) return 'processing'
  if (recognitionTasks.value.some(t => t.status === 'completed')) return 'completed'
  if (recognitionTasks.value.some(t => t.status === 'error')) return 'error'
  return 'idle'
})

const canStartRecognition = computed(() => {
  return fileList.value.length > 0 &&
    processingOptions.language &&
    (processingOptions.outputFormats?.length || 0) > 0 &&
    outputDirectory.value &&
    !isRecognizing.value
})

const hasVideoFiles = computed(() => {
  const videoFormats = ['mp4', 'avi', 'mov', 'mkv', 'flv', 'wmv']
  return fileList.value.some(file => {
    const ext = file.name.split('.').pop()?.toLowerCase()
    return videoFormats.includes(ext || '')
  })
})

// 事件处理方法
const handleFileAdded = (file: UploadFile, filePath: string) => {
  filePathMap.value.set(file.uid.toString(), filePath)

  // 设置默认输出目录
  if (!outputDirectory.value) {
    outputDirectory.value = mediaStore.settings.defaultOutputDir
  }

  emit('fileUploaded', {
    name: file.name,
    size: file.size,
    path: filePath
  })
}

const handleFileRemoved = (file: UploadFile) => {
  filePathMap.value.delete(file.uid.toString())

  // 如果没有文件了，清空相关数据
  if (fileList.value.length === 0) {
    recognitionTasks.value = []
    asrResults.value = []
    currentResult.value = null
    isRecognizing.value = false
  }
}

const handleDirectorySelected = (directory: string) => {
  console.log('输出目录已选择:', directory)
}

const handleError = (message: string) => {
  ElMessage.error(message)
}

const handlePresetSaved = (preset: any) => {
  ElMessage.success(`预设"${preset.label}"已保存`)
}

// 识别控制方法
const startRecognition = async () => {
  if (!canStartRecognition.value) {
    ElMessage.warning('请检查识别设置')
    return
  }

  isRecognizing.value = true
  recognitionTasks.value = []
  currentResult.value = null

  try {
    // 为每个文件创建识别任务
    for (const file of fileList.value) {
      const filePath = filePathMap.value.get(file.uid.toString())
      if (!filePath) {
        ElMessage.warning(`未找到文件 ${file.name} 的路径`)
        continue
      }

      // 生成输出文件名（多格式）
      const outputPaths = generateOutputPaths(file.name)

      // 使用MediaStore创建单文件任务
      const taskId = await mediaStore.createSingleTask({
        type: 'asr',
        fileName: file.name,
        filePath,
        outputPath: outputPaths[0], // 主要输出路径
        options: JSON.parse(JSON.stringify({
          ...processingOptions,
          outputPaths, // 传递所有输出路径
          outputDirectory: outputDirectory.value // 添加输出目录
        })),
        fileSize: file.size // 添加文件大小
      })

      // 创建本地任务追踪
      const localTask = {
        id: taskId,
        fileName: file.name,
        inputPath: filePath,
        status: 'pending',
        progress: 0,
        type: 'asr',
        estimatedTime: estimateProcessingTime(file.size || 0),
        currentStep: undefined
      }

      recognitionTasks.value.push(localTask)

      // 启动任务
      startSingleTask(taskId, localTask)
    }

    emit('recognitionStarted', {
      taskCount: fileList.value.length,
      settings: processingOptions
    })

  } catch (error: any) {
    isRecognizing.value = false
    ElMessage.error(`开始识别失败: ${error.message}`)
  }
}

const startSingleTask = async (taskId: string, localTask: any) => {
  try {
    localTask.status = 'processing'
    localTask.progress = 5
    localTask.currentStep = '准备识别...'

    // 启动任务状态监听
    const stopWatching = startTaskStatusWatcher(taskId)
    taskWatchers.value.set(taskId, stopWatching)

    // 使用MediaStore启动任务
    await mediaStore.startSingleTask(taskId)

    // 任务启动后，监听器会自动处理状态更新

  } catch (error: any) {
    localTask.status = 'error'
    localTask.error = error.message
    localTask.currentStep = '处理失败'
    ElMessage.error(`${localTask.fileName} 识别失败: ${error.message}`)

    // 检查是否所有任务完成
    checkAllTasksCompleted()
  }
}

// 启动任务状态监听器
const startTaskStatusWatcher = (taskId: string) => {
  console.log(`[ASRProcessor] 开始监听任务状态: ${taskId}`)

  const intervalId = setInterval(() => {
    const storeTask = mediaStore.singleTasks.get(taskId)
    if (!storeTask) {
      console.warn(`[ASRProcessor] 任务不存在于store中: ${taskId}`)
      console.log(`[ASRProcessor] 当前store中的任务:`, Array.from(mediaStore.singleTasks.keys()))
      return
    }

    // 找到 recognitionTasks 中对应的任务索引
    const taskIndex = recognitionTasks.value.findIndex(t => t.taskId === taskId)
    if (taskIndex === -1) {
      console.warn(`[ASRProcessor] 任务不存在于本地列表中: ${taskId}`)
      return
    }

    const currentLocalTask = recognitionTasks.value[taskIndex]

    // 只有状态真正改变时才更新
    if (currentLocalTask.status !== storeTask.status ||
      currentLocalTask.progress !== storeTask.progress ||
      currentLocalTask.currentStep !== storeTask.currentStep) {

      // 创建更新后的任务对象
      const updatedTask = {
        ...currentLocalTask,
        status: storeTask.status,
        progress: storeTask.progress,
        currentStep: storeTask.currentStep,
        error: storeTask.error
      }

      // 使用响应式更新替换任务对象
      recognitionTasks.value.splice(taskIndex, 1, updatedTask)

      console.log(`[ASRProcessor] 任务状态更新: ${taskId} -> ${storeTask.status} (${storeTask.progress}%) - 结果: ${!!storeTask.result}`)
    }

    // 处理任务完成
    if (storeTask.status === 'completed') {
      console.log(`[ASRProcessor] 检测到任务完成: ${taskId}, 结果存在: ${!!storeTask.result}`)

      if (storeTask.result) {
        // 添加到结果列表（避免重复添加）
        const existingResult = asrResults.value.find(r => r.id === storeTask.result?.id)
        if (!existingResult && storeTask.result) {
          asrResults.value.push(storeTask.result)
        }

        // 如果是第一个完成的任务，显示结果
        if (!currentResult.value) {
          currentResult.value = storeTask.result.data
        }

        const currentTask = recognitionTasks.value[taskIndex]
        ElMessage.success(`${currentTask.fileName} 识别完成`)
        emit('recognitionCompleted', {
          taskId,
          result: storeTask.result
        })

        // 从fileList中移除已完成的文件
        const fileIndex = fileList.value.findIndex(f => f.name === currentTask.fileName)
        if (fileIndex > -1) {
          const removedFile = fileList.value[fileIndex]
          fileList.value.splice(fileIndex, 1)
          // 同时清理文件路径映射
          filePathMap.value.delete(removedFile.uid.toString())
          console.log(`[ASRProcessor] 已从文件列表移除: ${currentTask.fileName}`)
        }
      }

      // 延迟移除已完成任务，让用户看到完成状态
      setTimeout(() => {
        const finalTaskIndex = recognitionTasks.value.findIndex(t => t.taskId === taskId)
        if (finalTaskIndex > -1) {
          recognitionTasks.value.splice(finalTaskIndex, 1)
        }
      }, 2000) // 2秒后移除

      clearInterval(intervalId)
      taskWatchers.value.delete(taskId)
      checkAllTasksCompleted()

    } else if (storeTask.status === 'error') {
      const currentTask = recognitionTasks.value[taskIndex]
      ElMessage.error(`${currentTask.fileName} 识别失败: ${storeTask.error}`)

      // 延迟移除失败任务，让用户看到错误状态
      setTimeout(() => {
        const finalTaskIndex = recognitionTasks.value.findIndex(t => t.taskId === taskId)
        if (finalTaskIndex > -1) {
          recognitionTasks.value.splice(finalTaskIndex, 1)
        }
      }, 3000) // 3秒后移除

      clearInterval(intervalId)
      taskWatchers.value.delete(taskId)
      checkAllTasksCompleted()
    }
  }, 1000) // 每秒检查一次状态

  return () => clearInterval(intervalId)
}

const pauseRecognition = async () => {
  try {
    const processingTasks = recognitionTasks.value.filter(t => t.status === 'processing')

    for (const task of processingTasks) {
      await mediaStore.pauseSingleTask(task.id)
      task.status = 'paused'
      task.currentStep = '已暂停'
    }

    isRecognizing.value = false
    ElMessage.info('识别已暂停')
  } catch (error: any) {
    ElMessage.error(`暂停失败: ${error.message}`)
  }
}

const stopRecognition = async () => {
  try {
    await pauseRecognition()

    // 重置所有任务状态
    recognitionTasks.value.forEach(task => {
      if (['processing', 'paused'].includes(task.status)) {
        task.status = 'pending'
        task.progress = 0
        task.currentStep = undefined
      }
    })

    ElMessage.info('识别已停止')
  } catch (error: any) {
    ElMessage.error(`停止失败: ${error.message}`)
  }
}

// 任务操作方法
const retryTask = async (task: any) => {
  try {
    await mediaStore.retrySingleTask(task.id)

    const localTask = recognitionTasks.value.find(t => t.id === task.id)
    if (localTask) {
      localTask.status = 'pending'
      localTask.progress = 0
      localTask.error = undefined
      localTask.currentStep = undefined
    }

    ElMessage.success('任务重试已开始')
  } catch (error: any) {
    ElMessage.error(`重试失败: ${error.message}`)
  }
}

const pauseTask = async (task: any) => {
  try {
    await mediaStore.pauseSingleTask(task.id)

    const localTask = recognitionTasks.value.find(t => t.id === task.id)
    if (localTask) {
      localTask.status = 'paused'
      localTask.currentStep = '已暂停'
    }

    ElMessage.info('任务已暂停')
  } catch (error: any) {
    ElMessage.error(`暂停失败: ${error.message}`)
  }
}

const removeTask = async (task: any) => {
  try {
    await mediaStore.removeSingleTask(task.id)

    const index = recognitionTasks.value.findIndex(t => t.id === task.id)
    if (index > -1) {
      recognitionTasks.value.splice(index, 1)
    }

    ElMessage.success('任务已移除')
  } catch (error: any) {
    ElMessage.error(`移除失败: ${error.message}`)
  }
}

const viewASRResult = (task: any) => {
  const storeTask = mediaStore.singleTasks.get(task.id)
  if (storeTask?.result?.data) {
    currentResult.value = storeTask.result.data
  }
}

// 结果操作方法
const retryTaskFromResult = async (result: any) => {
  const localTask = recognitionTasks.value.find(t => t.fileName === result.fileName)
  if (localTask) {
    await retryTask(localTask)
  }
}

const removeResult = (result: any) => {
  const index = asrResults.value.findIndex(r => r.id === result.id)
  if (index > -1) {
    asrResults.value.splice(index, 1)
  }

  // 如果移除的是当前显示的结果，清空显示
  if (currentResult.value && currentResult.value.id === result.id) {
    currentResult.value = null
  }
}

const copyToClipboard = async (text: string) => {
  try {
    await window.electronAPI.app.clipboardWriteText(text)
    ElMessage.success('已复制到剪贴板')
  } catch (error: any) {
    ElMessage.error(`复制失败: ${error.message}`)
  }
}

const saveResultToFile = async (content: string, format: string) => {
  try {
    const result = await window.electronAPI.app.showSaveDialog({
      title: '保存识别结果',
      defaultPath: `recognition_result.${format}`,
      filters: [
        { name: `${format.toUpperCase()} 文件`, extensions: [format] }
      ]
    })

    if (!result.canceled && result.filePath) {
      // 调用文件系统API保存文件
      await window.electronAPI.fileSystem.writeFile(result.filePath, content)
      ElMessage.success('文件保存成功')

      // 询问是否打开文件所在文件夹
      const openFolder = await ElMessageBox.confirm(
        '文件已保存成功，是否打开文件所在文件夹？',
        '保存成功',
        {
          confirmButtonText: '打开文件夹',
          cancelButtonText: '取消',
          type: 'success'
        }
      ).catch(() => false)

      if (openFolder) {
        await window.electronAPI.app.showItemInFolder(result.filePath)
      }
    }
  } catch (error: any) {
    ElMessage.error(`保存失败: ${error.message}`)
  }
}

const exportAllResults = async () => {
  const completedTasks = recognitionTasks.value.filter(t => t.status === 'completed')

  if (completedTasks.length === 0) {
    ElMessage.warning('没有可导出的结果')
    return
  }

  try {
    // TODO: 实现批量导出逻辑
    ElMessage.success(`导出完成，共 ${completedTasks.length} 个文件`)
  } catch (error: any) {
    ElMessage.error(`导出失败: ${error.message}`)
  }
}

// 批量操作方法
const createBatchTask = async () => {
  if (fileList.value.length === 0) {
    ElMessage.warning('请先添加文件')
    return
  }

  try {
    const files = fileList.value.map(file => ({
      fileName: file.name,
      filePath: filePathMap.value.get(file.uid.toString()) || file.name
    }))

    const batchId = await mediaStore.createBatchTask({
      name: `语音识别批量任务_${new Date().toLocaleString()}`,
      type: 'asr',
      files,
      outputDirectory: outputDirectory.value,
      options: JSON.parse(JSON.stringify(processingOptions))
    })

    ElMessage.success('批量任务已创建')
    clearAll()

  } catch (error: any) {
    ElMessage.error(`创建批量任务失败: ${error.message}`)
  }
}

// 工具方法
const generateOutputPaths = (inputName: string): string[] => {
  const baseName = inputName.substring(0, inputName.lastIndexOf('.'))
  const paths: string[] = []

  processingOptions.outputFormats?.forEach(format => {
    const fileName = `${baseName}.${format}`
    paths.push(`${outputDirectory.value}/${fileName}`)
  })

  return paths
}

const estimateProcessingTime = (fileSize: number): number => {
  // 粗略估算：每MB大约需要10秒处理时间
  const fileSizeMB = fileSize / 1024 / 1024
  return Math.ceil(fileSizeMB * 10)
}

const checkAllTasksCompleted = () => {
  const allCompleted = recognitionTasks.value.every(t =>
    ['completed', 'error'].includes(t.status)
  )

  if (allCompleted) {
    isRecognizing.value = false

    const completedCount = recognitionTasks.value.filter(t => t.status === 'completed').length
    const failedCount = recognitionTasks.value.filter(t => t.status === 'error').length

    ElMessage.success(`识别完成: ${completedCount} 成功, ${failedCount} 失败`)
  }
}

const clearAll = () => {
  fileList.value = []
  recognitionTasks.value = []
  asrResults.value = []
  currentResult.value = null
  isRecognizing.value = false
  filePathMap.value.clear()
}

// 生命周期
onMounted(async () => {
  if (!mediaStore.isInitialized) {
    await mediaStore.initialize()
  }
})

onUnmounted(() => {
  // 清理所有任务监听器
  taskWatchers.value.forEach(stopWatcher => stopWatcher())
  taskWatchers.value.clear()
})
</script>

<style scoped lang="scss">
@use '@/assets/styles/index.scss' as *;

.asr-processor {
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-base;

    h3 {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 0;
      color: $text-primary;
      font-size: 16px;
      font-weight: 500;

      .section-icon {
        color: #13c2c2;
        font-size: 18px;
      }
    }
  }

  .settings-section,
  .progress-section,
  .results-section,
  .current-result-section {
    margin-top: $spacing-base;
  }

  .video-info-panel {
    margin-top: $spacing-base;

    .info-content {
      .info-item {
        display: flex;
        align-items: center;
        gap: $spacing-small;
        margin-bottom: $spacing-small;
        font-size: 13px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  .batch-section {
    margin-top: $spacing-base;

    .batch-actions {
      display: flex;
      gap: $spacing-base;
      justify-content: center;
    }
  }
}

// ASR结果查看器样式
.asr-result-viewer {
  background: $background-light;
  border-radius: $border-radius-base;
  padding: $spacing-base;

  .result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-base;

    h4 {
      margin: 0;
      color: $text-primary;
    }
  }

  .result-content {
    .result-actions {
      display: flex;
      gap: $spacing-small;
      margin-bottom: $spacing-base;
    }

    .result-textarea {
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 14px;
      line-height: 1.6;
    }
  }

  .stats-display {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: $spacing-base;

    .stat-item {
      display: flex;
      justify-content: space-between;
      padding: $spacing-small $spacing-base;
      background: $background-card;
      border-radius: $border-radius-small;

      .stat-label {
        color: $text-secondary;
      }

      .stat-value {
        font-weight: 600;
        color: #13c2c2;
      }
    }
  }
}
</style>