<template>
  <div class="asr-result-viewer">
    <div class="result-header">
      <h4>
        <Icon icon="mdi:text-recognition" class="header-icon" />
        识别结果
      </h4>
      <div class="header-actions">
        <el-button size="small" @click="copyText" :disabled="!result.text">
          <Icon icon="mdi:content-copy" />
          复制文本
        </el-button>
        <el-button size="small" @click="openOutputFolder" v-if="outputDirectory">
          <Icon icon="mdi:folder-open" />
          打开文件夹
        </el-button>
        <el-button size="small" @click="$emit('close')" type="info">
          <Icon icon="mdi:close" />
          关闭
        </el-button>
      </div>
    </div>

    <div class="result-content">
      <!-- 文本结果 -->
      <div class="text-section" v-if="result.text">
        <div class="section-header">
          <h5>
            <Icon icon="mdi:text" />
            识别文本
          </h5>
          <div class="section-actions">
            <el-button size="small" @click="saveFile('text')">
              <Icon icon="mdi:download" />
              保存TXT
            </el-button>
          </div>
        </div>
        <div class="text-content">
          <el-input
            v-model="result.text"
            type="textarea"
            :rows="8"
            readonly
            resize="vertical"
            placeholder="识别文本将显示在这里..."
          />
        </div>
      </div>

      <!-- SRT字幕 -->
      <div class="srt-section" v-if="result.srt">
        <div class="section-header">
          <h5>
            <Icon icon="mdi:subtitles" />
            SRT字幕
          </h5>
          <div class="section-actions">
            <el-button size="small" @click="saveFile('srt')">
              <Icon icon="mdi:download" />
              保存SRT
            </el-button>
          </div>
        </div>
        <div class="srt-content">
          <el-input
            v-model="result.srt"
            type="textarea"
            :rows="6"
            readonly
            resize="vertical"
            placeholder="SRT字幕将显示在这里..."
          />
        </div>
      </div>

      <!-- VTT字幕 -->
      <div class="vtt-section" v-if="result.vtt">
        <div class="section-header">
          <h5>
            <Icon icon="mdi:subtitles-outline" />
            VTT字幕
          </h5>
          <div class="section-actions">
            <el-button size="small" @click="saveFile('vtt')">
              <Icon icon="mdi:download" />
              保存VTT
            </el-button>
          </div>
        </div>
        <div class="vtt-content">
          <el-input
            v-model="result.vtt"
            type="textarea"
            :rows="6"
            readonly
            resize="vertical"
            placeholder="VTT字幕将显示在这里..."
          />
        </div>
      </div>

      <!-- 时间轴信息 -->
      <div class="segments-section" v-if="result.segments && result.segments.length > 0">
        <div class="section-header">
          <h5>
            <Icon icon="mdi:timeline" />
            时间轴信息
          </h5>
          <div class="section-actions">
            <el-button size="small" @click="saveFile('json')">
              <Icon icon="mdi:download" />
              保存JSON
            </el-button>
          </div>
        </div>
        <div class="segments-content">
          <el-table :data="result.segments" size="small" max-height="300">
            <el-table-column prop="start" label="开始时间" width="100">
              <template #default="{ row }">
                {{ formatTime(row.start) }}
              </template>
            </el-table-column>
            <el-table-column prop="end" label="结束时间" width="100">
              <template #default="{ row }">
                {{ formatTime(row.end) }}
              </template>
            </el-table-column>
            <el-table-column prop="text" label="文本内容" min-width="200" />
          </el-table>
        </div>
      </div>

      <!-- 统计信息 -->
      <div class="statistics-section" v-if="result.statistics">
        <div class="section-header">
          <h5>
            <Icon icon="mdi:chart-line" />
            统计信息
          </h5>
        </div>
        <div class="statistics-content">
          <el-descriptions :column="2" size="small" border>
            <el-descriptions-item label="总时长">
              {{ formatTime(result.statistics.totalDuration) }}
            </el-descriptions-item>
            <el-descriptions-item label="字符数">
              {{ result.statistics.totalCharacters }}
            </el-descriptions-item>
            <el-descriptions-item label="词数">
              {{ result.statistics.totalWords }}
            </el-descriptions-item>
            <el-descriptions-item label="段落数">
              {{ result.statistics.totalSegments }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Icon } from '@iconify/vue'

// Props
interface Props {
  result: {
    text?: string
    srt?: string
    vtt?: string
    segments?: Array<{
      start: number
      end: number
      text: string
      duration?: number
    }>
    statistics?: {
      totalDuration: number
      totalCharacters: number
      totalWords: number
      totalSegments: number
    }
  }
  outputDirectory?: string
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  copyText: [text: string]
  saveFile: [content: string, format: string]
  close: []
}>()

// 复制文本到剪贴板
const copyText = async () => {
  if (props.result.text) {
    emit('copyText', props.result.text)
  }
}

// 保存文件
const saveFile = (format: string) => {
  let content = ''
  
  switch (format) {
    case 'text':
      content = props.result.text || ''
      break
    case 'srt':
      content = props.result.srt || ''
      break
    case 'vtt':
      content = props.result.vtt || ''
      break
    case 'json':
      content = JSON.stringify(props.result, null, 2)
      break
    default:
      ElMessage.error('不支持的文件格式')
      return
  }
  
  if (content) {
    emit('saveFile', content, format)
  } else {
    ElMessage.warning(`没有${format.toUpperCase()}内容可保存`)
  }
}

// 打开输出文件夹
const openOutputFolder = async () => {
  if (props.outputDirectory) {
    try {
      await window.electronAPI.app.showItemInFolder(props.outputDirectory)
    } catch (error: any) {
      ElMessage.error(`打开文件夹失败: ${error.message}`)
    }
  }
}

// 格式化时间
const formatTime = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)
  const ms = Math.floor((seconds % 1) * 1000)
  
  if (hours > 0) {
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}.${ms.toString().padStart(3, '0')}`
  } else {
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}.${ms.toString().padStart(3, '0')}`
  }
}
</script>

<style scoped>
.asr-result-viewer {
  border: 1px solid var(--el-border-color);
  border-radius: 8px;
  overflow: hidden;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: var(--el-bg-color-page);
  border-bottom: 1px solid var(--el-border-color);
}

.result-header h4 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--el-text-color-primary);
}

.header-icon {
  color: var(--el-color-primary);
}

.header-actions {
  display: flex;
  gap: 8px;
}

.result-content {
  padding: 16px;
}

.text-section,
.srt-section,
.vtt-section,
.segments-section,
.statistics-section {
  margin-bottom: 24px;
}

.text-section:last-child,
.srt-section:last-child,
.vtt-section:last-child,
.segments-section:last-child,
.statistics-section:last-child {
  margin-bottom: 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.section-header h5 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--el-text-color-regular);
  font-size: 14px;
}

.section-actions {
  display: flex;
  gap: 8px;
}

.text-content,
.srt-content,
.vtt-content {
  margin-bottom: 16px;
}

.segments-content {
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  overflow: hidden;
}

.statistics-content {
  background: var(--el-bg-color-page);
  padding: 12px;
  border-radius: 4px;
}
</style>
