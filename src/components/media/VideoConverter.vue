<template>
  <div class="app-card video-converter">
    <div class="section-header">
      <h3>
        <Icon icon="mdi:video-outline" class="section-icon" />
        音视频转换
      </h3>
      <el-button size="small" @click="clearAll" v-if="fileList.length > 0">
        <Icon icon="mdi:trash-can-outline" />
        清空
      </el-button>
    </div>

    <!-- 文件上传 -->
    <FileUploader v-model="fileList" task-type="video-convert" :max-file-size="1024" upload-text="拖拽视频文件到此处或点击上传"
      hint="支持 MP4, AVI, MOV, MKV, FLV, WMV 格式，最大 1GB" @file-added="handleFileAdded" @file-removed="handleFileRemoved"
      @error="handleError" />

    <!-- 处理选项 -->
    <div class="options-section" v-if="fileList.length > 0">
      <el-divider>转换设置</el-divider>
      <ProcessingOptions v-model="processingOptions" task-type="video-convert" :show-presets="true" />
    </div>

    <!-- 输出目录 -->
    <OutputDirectorySelector v-model="outputDirectory" v-if="fileList.length > 0"
      @directory-selected="handleDirectorySelected" @error="handleError" />

    <!-- 任务控制 -->
    <TaskControls v-if="fileList.length > 0" mode="single" :status="taskStatus" :is-processing="isProcessing"
      :can-start="!!canStartConversion" :can-pause="isProcessing" :can-stop="isProcessing" :show-batch-controls="false"
      :show-advanced="false" start-button-text="开始转换" processing-button-text="转换中..." @start="startConversion"
      @pause="pauseConversion" @stop="stopConversion" />

    <!-- 任务进度 -->
    <div class="progress-section" v-if="currentTasks.length > 0">
      <el-divider>转换进度</el-divider>
      <TaskProgress :tasks="currentTasks" :allow-retry="true" :allow-pause="true" :allow-remove="true"
        :show-summary="currentTasks.length > 1" @retry="retryTask" @pause="pauseTask" @remove="removeTask"
        @view-result="viewResult" />
    </div>

    <!-- 结果面板 -->
    <div class="results-section" v-if="taskResults.length > 0">
      <el-divider>处理结果</el-divider>
      <TaskResultPanel :results="taskResults" :allow-retry="true" empty-text="暂无转换结果" empty-hint="完成转换后结果将显示在这里"
        @retry="retryTaskFromResult" @remove="removeResult" @export-all="exportAllResults" />
    </div>

    <!-- 批量操作（文件数 > 1 时显示） -->
    <div class="batch-section" v-if="fileList.length > 1">
      <el-divider>批量操作</el-divider>
      <div class="batch-actions">
        <el-button @click="createBatchTask">
          <Icon icon="mdi:playlist-plus" />
          创建批量任务
        </el-button>
        <el-button @click="addToGlobalQueue">
          <Icon icon="mdi:plus-circle-outline" />
          添加到全局队列
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Icon } from '@iconify/vue'
import type { UploadFile } from 'element-plus'

// 导入共享组件
import FileUploader from './shared/FileUploader.vue'
import OutputDirectorySelector from './shared/OutputDirectorySelector.vue'
import ProcessingOptions from './shared/ProcessingOptions.vue'
import TaskControls from './shared/TaskControls.vue'
import TaskProgress from './shared/TaskProgress.vue'
import TaskResultPanel from './shared/TaskResultPanel.vue'

// 导入Store
import { useMediaStore } from '@/stores/media-store'
import type { ProcessingOptions as ProcessingOptionsType } from '@/stores/media-store'

// 定义事件
const emit = defineEmits<{
  fileUploaded: [fileInfo: any]
  conversionStarted: [taskInfo: any]
  conversionCompleted: [result: any]
}>()

// 响应式数据
const mediaStore = useMediaStore()
const fileList = ref<UploadFile[]>([])
const outputDirectory = ref('')
const currentTasks = ref<any[]>([])
const taskResults = ref<any[]>([])
const isProcessing = ref(false)
const filePathMap = ref<Map<string, string>>(new Map())
const taskWatchers = ref<Map<string, () => void>>(new Map())

// 处理选项
const processingOptions = reactive<ProcessingOptionsType>({
  outputFormat: 'mp3',
  quality: '192k',
  resizeEnabled: false,
  maxWidth: 1280
})

// 计算属性
const taskStatus = computed(() => {
  if (isProcessing.value) return 'processing'
  if (currentTasks.value.some(t => t.status === 'completed')) return 'completed'
  if (currentTasks.value.some(t => t.status === 'error')) return 'error'
  return 'idle'
})

const canStartConversion = computed(() => {
  return fileList.value.length > 0 &&
    outputDirectory.value &&
    !isProcessing.value
})

// 事件处理方法
const handleFileAdded = (file: UploadFile, filePath: string) => {
  filePathMap.value.set(file.uid.toString(), filePath)

  // 设置默认输出目录
  if (!outputDirectory.value) {
    outputDirectory.value = mediaStore.settings.defaultOutputDir
  }

  emit('fileUploaded', {
    name: file.name,
    size: file.size,
    path: filePath
  })
}

const handleFileRemoved = (file: UploadFile) => {
  filePathMap.value.delete(file.uid.toString())

  // 如果没有文件了，清空相关数据
  if (fileList.value.length === 0) {
    currentTasks.value = []
    taskResults.value = []
    isProcessing.value = false
  }
}

const handleDirectorySelected = (directory: string) => {
  console.log('输出目录已选择:', directory)
}

const handleError = (message: string) => {
  ElMessage.error(message)
}

// 任务控制方法
const startConversion = async () => {
  if (!canStartConversion.value) {
    ElMessage.warning('请检查转换设置')
    return
  }

  isProcessing.value = true
  currentTasks.value = []

  try {
    // 为每个文件创建任务
    for (const file of fileList.value) {
      const filePath = filePathMap.value.get(file.uid.toString())
      if (!filePath) {
        ElMessage.warning(`未找到文件 ${file.name} 的路径`)
        continue
      }

      const outputFileName = generateOutputFileName(file.name)
      const outputPath = `${outputDirectory.value}/${outputFileName}`

      // 使用MediaStore创建单文件任务
      const taskId = await mediaStore.createSingleTask({
        type: 'video-convert',
        fileName: file.name,
        filePath,
        outputPath,
        options: JSON.parse(JSON.stringify(processingOptions)),
        fileSize: file.size // 添加文件大小
      })

      // 创建本地任务追踪
      const localTask = {
        id: taskId,
        fileName: file.name,
        inputPath: filePath,
        outputPath,
        status: 'pending',
        progress: 0,
        type: 'video-convert'
      }

      currentTasks.value.push(localTask)

      // 启动任务
      startSingleTask(taskId, localTask)
    }

    emit('conversionStarted', {
      taskCount: fileList.value.length,
      settings: processingOptions
    })

  } catch (error: any) {
    isProcessing.value = false
    ElMessage.error(`开始转换失败: ${error.message}`)
  }
}

const startSingleTask = async (taskId: string, localTask: any) => {
  try {
    localTask.status = 'processing'

    // 启动任务状态监听
    const stopWatching = startTaskStatusWatcher(taskId)
    taskWatchers.value.set(taskId, stopWatching)

    // 使用MediaStore启动任务
    await mediaStore.startSingleTask(taskId)

    // 任务启动后，监听器会自动处理状态更新

  } catch (error: any) {
    localTask.status = 'error'
    localTask.error = error.message
    ElMessage.error(`${localTask.fileName} 转换失败: ${error.message}`)

    // 检查是否所有任务完成
    checkAllTasksCompleted()
  }
}

// 启动任务状态监听器
const startTaskStatusWatcher = (taskId: string) => {
  const intervalId = setInterval(() => {
    const storeTask = mediaStore.singleTasks.get(taskId)
    if (!storeTask) return

    // 找到 currentTasks 中对应的任务索引
    const taskIndex = currentTasks.value.findIndex(t => t.id === taskId)
    if (taskIndex === -1) return

    // 创建更新后的任务对象
    const updatedTask = {
      ...currentTasks.value[taskIndex],
      status: storeTask.status,
      progress: storeTask.progress,
      error: storeTask.error,
      currentStep: storeTask.currentStep
    }

    // 使用响应式更新替换任务对象
    currentTasks.value.splice(taskIndex, 1, updatedTask)

    // 处理任务完成
    if (storeTask.status === 'completed' && storeTask.result) {
      // 添加到结果列表（避免重复添加）
      const existingResult = taskResults.value.find(r => r.id === storeTask.result?.id)
      if (!existingResult && storeTask.result) {
        taskResults.value.push(storeTask.result)
      }

      ElMessage.success(`${updatedTask.fileName} 转换完成`)
      emit('conversionCompleted', {
        taskId,
        result: storeTask.result
      })

      // 从fileList中移除已完成的文件
      const fileIndex = fileList.value.findIndex(f => f.name === updatedTask.fileName)
      if (fileIndex > -1) {
        const removedFile = fileList.value[fileIndex]
        fileList.value.splice(fileIndex, 1)
        // 同时清理文件路径映射
        filePathMap.value.delete(removedFile.uid.toString())
        console.log(`[VideoConverter] 已从文件列表移除: ${updatedTask.fileName}`)
      }

      // 延迟移除已完成任务，让用户看到完成状态
      setTimeout(() => {
        const finalTaskIndex = currentTasks.value.findIndex(t => t.id === taskId)
        if (finalTaskIndex > -1) {
          currentTasks.value.splice(finalTaskIndex, 1)
        }
      }, 2000) // 2秒后移除

      clearInterval(intervalId)
      taskWatchers.value.delete(taskId)
      checkAllTasksCompleted()

    } else if (storeTask.status === 'error') {
      ElMessage.error(`${updatedTask.fileName} 转换失败: ${storeTask.error}`)

      clearInterval(intervalId)
      taskWatchers.value.delete(taskId)
      checkAllTasksCompleted()
    }
  }, 1000) // 每秒检查一次状态

  return () => clearInterval(intervalId)
}

const pauseConversion = async () => {
  try {
    // 暂停所有处理中的任务
    const processingTasks = currentTasks.value.filter(t => t.status === 'processing')

    for (const task of processingTasks) {
      await mediaStore.pauseSingleTask(task.id)
      task.status = 'paused'
    }

    isProcessing.value = false
    ElMessage.info('转换已暂停')
  } catch (error: any) {
    ElMessage.error(`暂停失败: ${error.message}`)
  }
}

const stopConversion = async () => {
  try {
    // 暂停所有任务
    await pauseConversion()

    // 重置所有任务状态
    currentTasks.value.forEach(task => {
      if (task.status === 'processing' || task.status === 'paused') {
        task.status = 'pending'
        task.progress = 0
      }
    })

    ElMessage.info('转换已停止')
  } catch (error: any) {
    ElMessage.error(`停止失败: ${error.message}`)
  }
}

// 任务操作方法
const retryTask = async (task: any) => {
  try {
    await mediaStore.retrySingleTask(task.id)

    // 更新本地任务状态
    const localTask = currentTasks.value.find(t => t.id === task.id)
    if (localTask) {
      localTask.status = 'pending'
      localTask.progress = 0
      localTask.error = undefined
    }

    ElMessage.success('任务重试已开始')
  } catch (error: any) {
    ElMessage.error(`重试失败: ${error.message}`)
  }
}

const pauseTask = async (task: any) => {
  try {
    await mediaStore.pauseSingleTask(task.id)

    // 更新本地任务状态
    const localTask = currentTasks.value.find(t => t.id === task.id)
    if (localTask) {
      localTask.status = 'paused'
    }

    ElMessage.info('任务已暂停')
  } catch (error: any) {
    ElMessage.error(`暂停失败: ${error.message}`)
  }
}

const removeTask = async (task: any) => {
  try {
    await mediaStore.removeSingleTask(task.id)

    // 从本地任务列表中移除
    const index = currentTasks.value.findIndex(t => t.id === task.id)
    if (index > -1) {
      currentTasks.value.splice(index, 1)
    }

    ElMessage.success('任务已移除')
  } catch (error: any) {
    ElMessage.error(`移除失败: ${error.message}`)
  }
}

const viewResult = async (task: any) => {
  const storeTask = mediaStore.singleTasks.get(task.id)
  if (storeTask?.result?.outputPath) {
    try {
      await window.electronAPI.app.showItemInFolder(storeTask.result.outputPath)
    } catch (error: any) {
      ElMessage.error(`打开文件失败: ${error.message}`)
    }
  }
}

// 结果操作方法
const retryTaskFromResult = async (result: any) => {
  // 从结果中找到对应的任务并重试
  const localTask = currentTasks.value.find(t => t.fileName === result.fileName)
  if (localTask) {
    await retryTask(localTask)
  }
}

const removeResult = (result: any) => {
  const index = taskResults.value.findIndex(r => r.id === result.id)
  if (index > -1) {
    taskResults.value.splice(index, 1)
  }
}

const exportAllResults = () => {
  // TODO: 实现批量导出功能
  ElMessage.info('批量导出功能开发中...')
}

// 批量操作方法
const createBatchTask = async () => {
  if (fileList.value.length === 0) {
    ElMessage.warning('请先添加文件')
    return
  }

  try {
    const files = fileList.value.map(file => ({
      fileName: file.name,
      filePath: filePathMap.value.get(file.uid.toString()) || file.name
    }))

    const batchId = await mediaStore.createBatchTask({
      name: `视频转换批量任务_${new Date().toLocaleString()}`,
      type: 'video-convert',
      files,
      outputDirectory: outputDirectory.value,
      options: JSON.parse(JSON.stringify(processingOptions))
    })

    ElMessage.success('批量任务已创建')

    // 清空当前文件列表，因为已经加入批量队列
    clearAll()

    // 导航到批量处理页面（可选）
    // router.push('/media?tab=batch')

  } catch (error: any) {
    ElMessage.error(`创建批量任务失败: ${error.message}`)
  }
}

const addToGlobalQueue = () => {
  // TODO: 实现添加到全局队列功能
  ElMessage.info('添加到全局队列功能开发中...')
}

// 工具方法
const generateOutputFileName = (inputName: string): string => {
  const baseName = inputName.substring(0, inputName.lastIndexOf('.'))
  return `${baseName}.${processingOptions.outputFormat || 'mp4'}`
}

const checkAllTasksCompleted = () => {
  const allCompleted = currentTasks.value.every(t =>
    ['completed', 'error'].includes(t.status)
  )

  if (allCompleted) {
    isProcessing.value = false

    const completedCount = currentTasks.value.filter(t => t.status === 'completed').length
    const failedCount = currentTasks.value.filter(t => t.status === 'error').length

    ElMessage.success(`转换完成: ${completedCount} 成功, ${failedCount} 失败`)
  }
}

const clearAll = () => {
  // 清理所有任务监听器
  taskWatchers.value.forEach(stopWatcher => stopWatcher())
  taskWatchers.value.clear()

  fileList.value = []
  currentTasks.value = []
  taskResults.value = []
  isProcessing.value = false
  filePathMap.value.clear()
}

// 生命周期
onMounted(async () => {
  // 确保MediaStore已初始化
  if (!mediaStore.isInitialized) {
    await mediaStore.initialize()
  }
})

onUnmounted(() => {
  // 清理所有任务监听器
  taskWatchers.value.forEach(stopWatcher => stopWatcher())
  taskWatchers.value.clear()
})
</script>

<style scoped lang="scss">
@use '@/assets/styles/index.scss' as *;

.video-converter {
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-base;

    h3 {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 0;
      color: $text-primary;
      font-size: 16px;
      font-weight: 500;

      .section-icon {
        color: $primary-color;
        font-size: 18px;
      }
    }
  }

  .options-section,
  .progress-section,
  .results-section {
    margin-top: $spacing-base;
  }

  .batch-section {
    margin-top: $spacing-base;

    .batch-actions {
      display: flex;
      gap: $spacing-base;
      justify-content: center;
    }
  }
}
</style>