import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { emitStatsUpdate, emitTaskCompleted, emitTaskFailed, emitTaskStarted } from '@/utils/stats-event-bus'

// 任务相关类型定义
// 这些类型已经在 vite-env.d.ts 中全局声明，这里保留以保持代码可读性和兼容性
export interface BaseTask {
  id: string
  type: 'video-convert' | 'audio-extract' | 'asr' | 'image-process'
  fileName: string
  filePath: string
  outputPath: string
  status: 'pending' | 'processing' | 'completed' | 'error' | 'paused'
  progress: number
  error?: string
  createdAt: number
  startTime?: number
  completedAt?: number
  estimatedTime?: number
  currentStep?: string
  fileSize?: number // 原始文件大小（字节）
}

export interface SingleTask extends BaseTask {
  mode: 'single'
  options: ProcessingOptions
  result?: TaskResult
}

export interface BatchSubTask extends BaseTask {
  mode: 'batch'
  batchId: string
  batchName: string
  options: ProcessingOptions
  result?: TaskResult
}

export interface BatchTask {
  id: string
  name: string
  type: 'video-convert' | 'audio-extract' | 'asr' | 'image-process'
  status: 'pending' | 'processing' | 'completed' | 'error' | 'paused'
  progress: number
  tasks: BatchSubTask[]
  totalTasks: number
  completedTasks: number
  failedTasks: number
  createdAt: number
  startTime?: number
  completedAt?: number
  options: ProcessingOptions
  outputDirectory: string
}

export type MediaTask = SingleTask | BatchSubTask

export interface ProcessingOptions {
  outputFormat?: string
  quality?: string
  resizeEnabled?: boolean
  maxWidth?: number
  format?: string
  audioQuality?: string
  language?: string
  outputFormats?: string[]
  enableTimestamps?: boolean
  filterSilence?: boolean
  width?: number
  height?: number
  maintainAspectRatio?: boolean
  optimize?: boolean
  progressive?: boolean
  stripMetadata?: boolean
  colorSpace?: string
  saturation?: number
  [key: string]: any
}

export interface TaskResult {
  id: string
  fileName: string
  type: string
  success: boolean
  outputPath?: string
  completedAt: number
  size?: number
  duration?: number
  processingTime?: number // 处理时间（毫秒）
  error?: string
  data?: any
}

/**
 * 媒体任务管理 Store
 * 负责单文件任务和批量任务的状态管理
 */
export const useMediaTasksStore = defineStore('media-tasks', () => {
  // 核心状态
  const singleTasks = ref<Map<string, SingleTask>>(new Map())
  const batchTasks = ref<Map<string, BatchTask>>(new Map())
  const taskResults = ref<Map<string, TaskResult>>(new Map())
  const processingQueues = ref<Map<string, string[]>>(new Map()) // type -> taskIds
  const activeTasksCount = ref(0)

  // 单文件任务计算属性
  const allSingleTasks = computed(() => Array.from(singleTasks.value.values()))

  const activeSingleTasks = computed(() =>
    allSingleTasks.value.filter(task =>
      ['processing', 'pending'].includes(task.status)
    )
  )

  const completedSingleTasks = computed(() =>
    allSingleTasks.value.filter(task => task.status === 'completed')
  )

  const failedSingleTasks = computed(() =>
    allSingleTasks.value.filter(task => task.status === 'error')
  )

  // 批量任务计算属性
  const allBatchTasks = computed(() => Array.from(batchTasks.value.values()))

  const activeBatchTasks = computed(() =>
    allBatchTasks.value.filter(task =>
      ['processing', 'pending'].includes(task.status)
    )
  )

  // 所有任务计算属性
  const allTasks = computed(() => {
    const tasks: MediaTask[] = [...allSingleTasks.value]

    allBatchTasks.value.forEach(batch => {
      tasks.push(...batch.tasks)
    })

    return tasks
  })

  const activeTasks = computed(() =>
    allTasks.value.filter(task =>
      ['processing', 'pending'].includes(task.status)
    )
  )

  const completedTasks = computed(() =>
    allTasks.value.filter(task => task.status === 'completed')
  )

  const failedTasks = computed(() =>
    allTasks.value.filter(task => task.status === 'error')
  )

  const overallProgress = computed(() => {
    const tasks = allTasks.value
    if (tasks.length === 0) return 0
    const totalProgress = tasks.reduce((sum, task) => sum + task.progress, 0)
    return Math.floor(totalProgress / tasks.length)
  })

  // 结果相关计算属性
  const allResults = computed(() => Array.from(taskResults.value.values()))

  const successfulResults = computed(() =>
    allResults.value.filter(result => result.success)
  )

  const failedResults = computed(() =>
    allResults.value.filter(result => !result.success)
  )

  // 工具方法
  const generateTaskId = (): string => {
    return `task_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  }

  const generateBatchId = (): string => {
    return `batch_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  }

  const generateResultId = (): string => {
    return `result_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  }

  const generateOutputFileName = (inputName: string, taskType: string, options: ProcessingOptions): string => {
    const baseName = inputName.substring(0, inputName.lastIndexOf('.'))

    switch (taskType) {
      case 'video-convert':
        return `${baseName}.${options.outputFormat || 'mp4'}`
      case 'audio-extract':
        return `${baseName}.${options.format || 'mp3'}`
      case 'asr':
        return `${baseName}_transcript.txt`
      case 'image-process':
        return `${baseName}.${options.outputFormat || 'jpeg'}`
      default:
        return `${baseName}_processed`
    }
  }

  // 创建任务结果
  const createTaskResult = (task: MediaTask, response: any): TaskResult => {
    const now = Date.now()

    // 计算处理时间：如果任务有开始时间，使用当前时间减去开始时间
    const processingTime = task.startTime ? now - task.startTime : 0

    console.log(`[TasksStore] 创建任务结果: ${task.fileName}`)
    console.log(`[TasksStore] 开始时间: ${task.startTime}, 完成时间: ${now}`)
    console.log(`[TasksStore] 处理时间: ${processingTime}ms (${Math.floor(processingTime / 1000)}秒)`)

    return {
      id: generateResultId(),
      fileName: task.fileName,
      type: task.type,
      success: response.success,
      outputPath: response.success ? task.outputPath : undefined,
      completedAt: now,
      size: task.fileSize || 0, // 使用任务创建时的原始文件大小
      duration: response.data?.duration,
      processingTime, // 处理时间（毫秒）
      error: response.error,
      data: response.data
    }
  }

  // 更新处理队列
  const updateProcessingQueue = (taskType: string, taskId: string, action: 'add' | 'remove') => {
    const queue = processingQueues.value.get(taskType) || []

    if (action === 'add' && !queue.includes(taskId)) {
      queue.push(taskId)
    } else if (action === 'remove') {
      const index = queue.indexOf(taskId)
      if (index > -1) {
        queue.splice(index, 1)
      }
    }

    processingQueues.value.set(taskType, queue)
  }

  // 持久化单个任务
  const persistSingleTask = async (taskId: string): Promise<void> => {
    try {
      const task = singleTasks.value.get(taskId);
      if (task) {
        // 暂时禁用持久化，避免序列化问题
        console.log(`[TasksStore] 跳过持久化任务: ${taskId} (临时禁用)`);
        return;

        // 创建一个可序列化的任务副本
        const plainTask = {
          id: task.id,
          type: task.type,
          fileName: task.fileName,
          filePath: task.filePath,
          outputPath: task.outputPath,
          mode: task.mode,
          status: task.status,
          progress: task.progress,
          createdAt: task.createdAt,
          startTime: task.startTime,
          completedAt: task.completedAt,
          estimatedTime: task.estimatedTime,
          currentStep: task.currentStep,
          error: task.error,
          fileSize: task.fileSize,
          // 深拷贝options，确保可序列化
          options: JSON.parse(JSON.stringify(task.options || {}))
        };

        await window.electronAPI.media.persistTask(plainTask);
        console.log(`[TasksStore] 单文件任务已持久化: ${taskId}`);
      }
    } catch (error) {
      console.error(`[TasksStore] 持久化单文件任务失败: ${taskId}`, error);
    }
  }

  // 持久化批量任务
  const persistBatchTask = async (batchId: string): Promise<void> => {
    try {
      const batch = batchTasks.value.get(batchId);
      if (batch) {
        // 使用 electron-store 通过 IPC 持久化批量任务
        // 避免使用 JSON.parse(JSON.stringify()) 导致的类型擦除问题
        const plainBatch = { ...batch };
        await window.electronAPI.media.persistBatch(plainBatch);
        console.log(`[TasksStore] 批量任务已持久化: ${batchId}`);
      }
    } catch (error) {
      console.error(`[TasksStore] 持久化批量任务失败: ${batchId}`, error);
    }
  }

  // 持久化任务结果
  const persistTaskResult = async (resultId: string): Promise<void> => {
    try {
      const result = taskResults.value.get(resultId);
      if (result) {
        // 使用 electron-store 通过 IPC 持久化结果
        // 避免使用 JSON.parse(JSON.stringify()) 导致的类型擦除问题
        const plainResult = { ...result };
        await window.electronAPI.media.persistTaskResult(plainResult);
        console.log(`[TasksStore] 任务结果已持久化: ${resultId}`);
      }
    } catch (error) {
      console.error(`[TasksStore] 持久化任务结果失败: ${resultId}`, error);
    }
  }

  // 统一的任务状态更新方法，确保触发统计更新
  const updateTaskStatus = async (taskId: string, status: BaseTask['status'], progress?: number, error?: string): Promise<void> => {
    console.log(`[TasksStore] 更新任务状态: ${taskId} -> ${status}`)

    // 更新单文件任务
    const singleTask = singleTasks.value.get(taskId)
    if (singleTask) {
      singleTask.status = status
      if (progress !== undefined) singleTask.progress = progress
      if (error) singleTask.error = error
      if (status === 'processing' && !singleTask.startTime) {
        singleTask.startTime = Date.now()
      }
      if (status === 'completed' || status === 'error') {
        singleTask.completedAt = Date.now()
      }

      // 持久化更新
      await persistSingleTask(taskId)

      // 触发统计更新事件
      if (status === 'completed') {
        emitTaskCompleted(taskId, singleTask.type)
      } else if (status === 'error') {
        emitTaskFailed(taskId, singleTask.type)
      } else if (status === 'processing') {
        emitTaskStarted(taskId, singleTask.type)
      } else {
        emitStatsUpdate()
      }
      return
    }

    // 更新批量任务中的子任务
    for (const [batchId, batch] of batchTasks.value.entries()) {
      const subTask = batch.tasks.find(t => t.id === taskId)
      if (subTask) {
        subTask.status = status
        if (progress !== undefined) subTask.progress = progress
        if (error) subTask.error = error
        if (status === 'processing' && !subTask.startTime) {
          subTask.startTime = Date.now()
        }
        if (status === 'completed' || status === 'error') {
          subTask.completedAt = Date.now()
        }

        // 更新批量任务统计
        batch.completedTasks = batch.tasks.filter(t => t.status === 'completed').length
        batch.failedTasks = batch.tasks.filter(t => t.status === 'error').length
        batch.progress = Math.floor((batch.completedTasks + batch.failedTasks) / batch.totalTasks * 100)

        // 检查批量任务是否完成
        if (batch.completedTasks + batch.failedTasks === batch.totalTasks) {
          batch.status = batch.failedTasks > 0 ? 'error' : 'completed'
          batch.completedAt = Date.now()
        }

        // 持久化更新
        await persistBatchTask(batchId)

        // 触发统计更新事件
        if (status === 'completed') {
          emitTaskCompleted(taskId, subTask.type)
        } else if (status === 'error') {
          emitTaskFailed(taskId, subTask.type)
        } else if (status === 'processing') {
          emitTaskStarted(taskId, subTask.type)
        } else {
          emitStatsUpdate()
        }
        return
      }
    }

    console.warn(`[TasksStore] 未找到任务: ${taskId}`)
  }

  // 添加任务结果并触发统计更新
  const addTaskResult = async (result: TaskResult): Promise<void> => {
    console.log(`[TasksStore] 添加任务结果: ${result.id}`)
    taskResults.value.set(result.id, result)
    await persistTaskResult(result.id)

    // 触发统计更新
    emitStatsUpdate()
  }

  // 修改 createSingleTask 方法，添加持久化
  const createSingleTask = async (taskData: {
    type: 'video-convert' | 'audio-extract' | 'asr' | 'image-process'
    fileName: string
    filePath: string
    outputPath: string
    options: ProcessingOptions
    fileSize?: number // 添加文件大小参数
  }): Promise<string> => {
    const task: SingleTask = {
      ...taskData,
      id: generateTaskId(),
      mode: 'single',
      status: 'pending',
      progress: 0,
      createdAt: Date.now()
    }

    singleTasks.value.set(task.id, task);

    // 持久化新创建的任务
    await persistSingleTask(task.id);

    return task.id;
  }

  // 修改 removeSingleTask 方法，添加持久化
  const removeSingleTask = async (taskId: string): Promise<void> => {
    const task = singleTasks.value.get(taskId);
    if (!task) return;

    // 如果任务正在处理中，不允许删除
    if (task.status === 'processing') {
      throw new Error('无法删除正在处理中的任务');
    }

    singleTasks.value.delete(taskId);

    // 通知主进程删除任务
    try {
      await window.electronAPI.media.deleteTask(taskId);
      console.log(`[TasksStore] 单文件任务已删除: ${taskId}`);
    } catch (error) {
      console.error(`[TasksStore] 删除单文件任务失败: ${taskId}`, error);
    }
  }

  // 修改 createBatchTask 方法，添加持久化
  const createBatchTask = async (batchData: {
    name: string
    type: 'video-convert' | 'audio-extract' | 'asr' | 'image-process'
    files: Array<{ fileName: string; filePath: string; fileSize?: number }>
    outputDirectory: string
    options: ProcessingOptions
  }): Promise<string> => {
    const batchId = generateBatchId();

    const subTasks: BatchSubTask[] = batchData.files.map(file => {
      const outputFileName = generateOutputFileName(file.fileName, batchData.type, batchData.options);
      const outputPath = `${batchData.outputDirectory}/${outputFileName}`;

      return {
        id: generateTaskId(),
        mode: 'batch',
        batchId,
        batchName: batchData.name,
        type: batchData.type,
        fileName: file.fileName,
        filePath: file.filePath,
        outputPath,
        status: 'pending',
        progress: 0,
        createdAt: Date.now(),
        options: { ...batchData.options },
        fileSize: file.fileSize // 添加文件大小
      };
    });

    const batch: BatchTask = {
      id: batchId,
      name: batchData.name,
      type: batchData.type,
      status: 'pending',
      progress: 0,
      tasks: subTasks,
      totalTasks: subTasks.length,
      completedTasks: 0,
      failedTasks: 0,
      createdAt: Date.now(),
      options: { ...batchData.options },
      outputDirectory: batchData.outputDirectory
    };

    batchTasks.value.set(batchId, batch);

    // 持久化新创建的批量任务
    await persistBatchTask(batchId);

    return batchId;
  }

  // 修改 removeBatchTask 方法，添加持久化
  const removeBatchTask = async (batchId: string): Promise<void> => {
    const batch = batchTasks.value.get(batchId);
    if (!batch) return;

    // 如果批量任务正在处理中，不允许删除
    if (batch.status === 'processing') {
      throw new Error('无法删除正在处理中的批量任务');
    }

    batchTasks.value.delete(batchId);

    // 通知主进程删除批量任务
    try {
      await window.electronAPI.media.deleteBatch(batchId);
      console.log(`[TasksStore] 批量任务已删除: ${batchId}`);
    } catch (error) {
      console.error(`[TasksStore] 删除批量任务失败: ${batchId}`, error);
    }
  }

  // 修改 clearAllTasks 方法，添加持久化
  const clearAllTasks = async (): Promise<void> => {
    // 清空本地任务
    singleTasks.value.clear();
    batchTasks.value.clear();

    // 通知主进程清空所有任务
    try {
      await window.electronAPI.media.clearAllTasks();
      console.log('[TasksStore] 所有任务已清空');
    } catch (error) {
      console.error('[TasksStore] 清空所有任务失败', error);
    }
  }

  // 修改 clearResults 方法，添加持久化
  const clearResults = async (): Promise<void> => {
    // 清空本地结果
    taskResults.value.clear();

    // 通知主进程清空所有结果
    try {
      await window.electronAPI.media.clearTaskResults();
      console.log('[TasksStore] 所有任务结果已清空');
    } catch (error) {
      console.error('[TasksStore] 清空所有任务结果失败', error);
    }
  }

  return {
    // 状态
    singleTasks,
    batchTasks,
    taskResults,
    activeTasksCount,

    // 计算属性
    allSingleTasks,
    allBatchTasks,
    allTasks,
    activeTasks,
    completedTasks,
    failedTasks,
    overallProgress,
    allResults,
    successfulResults,
    failedResults,
    activeSingleTasks,
    activeBatchTasks,
    completedSingleTasks,
    failedSingleTasks,

    // 方法
    createSingleTask,
    createBatchTask,
    removeSingleTask,
    removeBatchTask,
    clearAllTasks,
    clearResults,
    createTaskResult,
    updateProcessingQueue,
    generateTaskId,
    generateBatchId,
    generateOutputFileName,

    // 添加新的持久化方法
    persistSingleTask,
    persistBatchTask,
    persistTaskResult,

    // 添加新的状态更新方法
    updateTaskStatus,
    addTaskResult
  }
})