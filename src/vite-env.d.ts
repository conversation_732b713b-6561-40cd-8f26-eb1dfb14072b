/// <reference types="vite/client" />
/// <reference types="node" />

// Define types for Electron API exposed via contextBridge
declare const __APP_VERSION__: string;

// 媒体进度数据接口
declare interface MediaProgressData {
    taskId: string;
    progress: number;
}

// FFmpeg状态接口
declare interface FFmpegStatus {
    available: boolean;
    version?: string;
    path?: string;
    error?: string;
}

// 从media-tasks.ts导入的类型
declare interface BaseTask {
    id: string;
    type: 'video-convert' | 'audio-extract' | 'asr' | 'image-process';
    fileName: string;
    filePath: string;
    outputPath: string;
    status: 'pending' | 'processing' | 'completed' | 'error' | 'paused';
    progress: number;
    error?: string;
    createdAt: number;
    startTime?: number;
    completedAt?: number;
    estimatedTime?: number;
    currentStep?: string;
}

declare interface SingleTask extends BaseTask {
    mode: 'single';
    options: ProcessingOptions;
    result?: TaskResult;
}

declare interface BatchSubTask extends BaseTask {
    mode: 'batch';
    batchId: string;
    batchName: string;
    options: ProcessingOptions;
    result?: TaskResult;
}

declare interface BatchTask {
    id: string;
    name: string;
    type: 'video-convert' | 'audio-extract' | 'asr' | 'image-process';
    status: 'pending' | 'processing' | 'completed' | 'error' | 'paused';
    progress: number;
    tasks: BatchSubTask[];
    totalTasks: number;
    completedTasks: number;
    failedTasks: number;
    createdAt: number;
    startTime?: number;
    completedAt?: number;
    options: ProcessingOptions;
    outputDirectory: string;
}

declare interface TaskResult {
    id: string;
    fileName: string;
    type: string;
    success: boolean;
    outputPath?: string;
    completedAt: number;
    size?: number;
    duration?: number;
    processingTime?: number; // 处理时间（毫秒）
    error?: string;
    data?: any;
}

declare interface ProcessingOptions {
    outputFormat?: string;
    quality?: string;
    resizeEnabled?: boolean;
    maxWidth?: number;
    format?: string;
    audioQuality?: string;
    language?: string;
    outputFormats?: string[];
    enableTimestamps?: boolean;
    filterSilence?: boolean;
    width?: number;
    height?: number;
    maintainAspectRatio?: boolean;
    optimize?: boolean;
    progressive?: boolean;
    stripMetadata?: boolean;
    colorSpace?: string;
    saturation?: number;
    [key: string]: any;
}

// Define interfaces for the structured electronAPI
interface IAppAPI {
    openDialog: (params: Electron.OpenDialogOptions) => Promise<{ canceled: boolean; filePaths?: string[] }>;
    showOpenDialog: (options: any) => Promise<{ canceled: boolean; filePaths?: string[] }>;
    showSaveDialog: (options: any) => Promise<{ canceled: boolean; filePath?: string }>;
    showItemInFolder: (path: string) => Promise<void>;
    openExternal: (url: string) => Promise<void>;
    clipboardWriteText: (text: string) => Promise<void>;
    getLoginInfo: () => Promise<any>; // TODO: Define a proper type for login info
    login: () => void;
    logout: () => void;
    showLoginWindow: (url?: string) => void;
    onLoginStatusChanged: (callback: () => void) => () => void;
}

interface IExcelAPI {
    fileParse: (filePath: string) => Promise<any>; // TODO: Define a proper type for parsed excel data
    fileBuild: (obj: { path: string, data: any }) => Promise<void>; // TODO: Define a proper type for excel data
    exportToExcel: (data: any, filePath: string) => Promise<void>; // TODO: Define a proper type for excel data
}

interface IFileSystemAPI {
    pathDirname: (filePath: string) => Promise<string>;
    pathBasename: (filePath: string) => Promise<string>;
    pathSep: () => Promise<string>;
    mkdirSync: (dirPath: string, options?: any) => Promise<void>; // 修改为any以避免fs依赖问题
    existsSync: (filePath: string) => Promise<boolean>;
    writeFile: (filePath: string, content: string) => Promise<{ success: boolean }>;
    readFile: (filePath: string) => Promise<string>;
}

interface IUpdaterAPI {
    checkForUpdates: (isManualCheck?: boolean) => void;
    downloadUpdate: (info: any) => void; // TODO: Define a proper type for update info

    onCheckingForUpdate: (callback: () => void) => () => void;
    onUpdateAvailable: (callback: (info: any, isManual: boolean) => void) => () => void; // TODO: Define a proper type for update info
    onUpdateNotAvailable: (callback: (isManual: boolean) => void) => () => void;
    onUpdateError: (callback: (error: string, isManual: boolean) => void) => () => void;
    onDownloadProgress: (callback: (progressObj: { progress: number; receivedBytes: number; totalBytes: number }) => void) => () => void;
    onDownloadCompleted: (callback: (filePath: string) => void) => () => void;
    onDownloadError: (callback: (error: string) => void) => () => void;
    onInstallError: (callback: (error: string) => void) => () => void;
    quitAndInstall: (filePath: string) => void;
}

interface IXhsAPI {
    noteDetail: (input: string) => Promise<any>; // TODO: Define a proper type for note detail
    noteImgs: (item: { text: string }) => Promise<any>; // TODO: Define a proper type for note images
    noteImgsSave: (item: { dir: string, imgs: any[], desc: string, title: string }) => Promise<void>; // TODO: Define proper types
    noteVideoSave: (item: { dir: string, video: any, title: string, desc: string }) => Promise<void>; // TODO: Define proper types
    notesSave: (item: { dir: string, notes: any[] }) => Promise<void>; // TODO: Define proper types
    notesFetch: (item: { keyword: string, page: number, type: string }) => Promise<any>; // TODO: Define proper types
    noteCheckWord: (text: string) => Promise<any>; // TODO: Define proper types
    noteCheckUser: () => Promise<any>; // TODO: Define proper types
    userInfo: (userId: string) => Promise<any>; // TODO: Define proper types
    noteCheck: (noteId: string, redId: string) => Promise<boolean>;
}

interface IMediaAPI {
    // 文件路径获取
    getPathForFile: (file: File) => string;

    // 音视频转换
    convertVideo: (inputPath: string, outputPath: string, options: any) => Promise<{ success: boolean; data?: any; error?: string }>;
    extractAudio: (videoPath: string, audioPath: string, options?: any) => Promise<{ success: boolean; data?: any; error?: string }>;

    // 语音识别
    extractText: (audioPath: string, options: any) => Promise<{ success: boolean; data?: any; error?: string }>;

    // 图片处理
    processImages: (imagePaths: string[], options: any) => Promise<{ success: boolean; data?: any; error?: string }>;

    // 批量任务管理
    createBatchTask: (tasks: any[]) => Promise<{ success: boolean; data?: any; error?: string }>;
    startBatchTask: (batchId: string) => Promise<{ success: boolean; data?: any; error?: string }>;
    getBatchProgress: (batchId: string) => Promise<{ success: boolean; data?: any; error?: string }>;
    pauseBatchTask: (batchId: string) => Promise<{ success: boolean; data?: any; error?: string }>;
    cancelBatchTask: (batchId: string) => Promise<{ success: boolean; data?: any; error?: string }>;

    // 单个任务控制
    retryTask: (batchId: string, taskId: string) => Promise<{ success: boolean; data?: any; error?: string }>;
    getTaskStatus: (taskId: string) => Promise<{ success: boolean; data?: any; error?: string }>;
    pauseTask: (taskId: string) => Promise<any>;
    resumeTask: (taskId: string) => Promise<any>;
    cancelTask: (taskId: string) => Promise<any>;

    // FFmpeg状态检查
    checkFFmpegStatus: () => Promise<{ success: boolean; data: FFmpegStatus }>;
    getActiveTasks: () => Promise<any>;

    // 进度监听
    onTaskProgress: (callback: (data: MediaProgressData) => void) => () => void;

    // 工具方法
    getSupportedFormats: () => Promise<{ success: boolean; data?: any; error?: string }>;
    getFileInfo: (filePath: string) => Promise<{ success: boolean; data?: any; error?: string }>;
    cleanupTemp: () => Promise<{ success: boolean; data?: any; error?: string }>;
    getStats: () => Promise<{ success: boolean; data?: any; error?: string }>;
    updateStats: () => Promise<any>;
    persistStats: (stats: any) => Promise<{ success: boolean; data?: any; error?: string }>;
    clearStats: () => Promise<any>;
    notifyStatsUpdate: () => Promise<{ success: boolean; data?: any; error?: string }>;

    // 设置管理
    getSettings: () => Promise<any>;
    saveSettings: (settings: any) => Promise<any>;

    // 任务持久化
    getUncompletedTasks: () => Promise<any>;
    persistTask: (task: SingleTask) => Promise<any>; // 使用具体类型
    persistBatch: (batch: BatchTask) => Promise<any>; // 使用具体类型
    deleteTask: (taskId: string) => Promise<any>;
    deleteBatch: (batchId: string) => Promise<any>;
    clearAllTasks: () => Promise<any>;

    // 任务结果管理
    getTaskResults: () => Promise<{ success: boolean; data?: any; error?: string }>;
    persistTaskResult: (result: TaskResult) => Promise<{ success: boolean; data?: any; error?: string }>; // 使用具体类型
    deleteTaskResult: (resultId: string) => Promise<{ success: boolean; data?: any; error?: string }>;
    clearTaskResults: () => Promise<{ success: boolean; data?: any; error?: string }>;
}


// Extend the Window interface to include the structured electronAPI
interface Window {
    ipcRenderer: {
        on(channel: string, listener: (event: Electron.IpcRendererEvent, ...args: any[]) => void): Electron.IpcRenderer;
        off(channel: string, listener: (...args: any[]) => void): Electron.IpcRenderer;
        send(channel: string, ...args: any[]): void;
        invoke(channel: string, ...args: any[]): Promise<any>;
    };
    electronAPI: {
        app: IAppAPI;
        excel: IExcelAPI;
        fileSystem: IFileSystemAPI;
        updater: IUpdaterAPI;
        xhs: IXhsAPI;
        media: IMediaAPI;
    };
}
